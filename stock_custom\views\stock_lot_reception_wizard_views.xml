<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Vue formulaire pour le wizard de création de lots de réception -->
    <record id="view_stock_lot_reception_wizard_form" model="ir.ui.view">
        <field name="name">stock.lot.reception.wizard.form</field>
        <field name="model">stock.lot.reception.wizard</field>
        <field name="arch" type="xml">
            <form string="Générer des numéros de lot">
                <group>
                    <group>
                        <field name="product_id" readonly="1"/>
                        <field name="first_lot_number"/>
                        <field name="quantity_received"/>
                        <field name="supplier_lot_number"/>
                        <field name="use_by_date"/>
                        <field name="reception_type"/>
                    </group>
                    <group>
                        <field name="manufacturing_date"/>
                        <field name="production_order_number"/>
                        <field name="package_type_id"/>
                        <field name="packages_per_pallet"/>
                        <field name="pallet_count"/>
                        <field name="is_complete_pallet"/>
                    </group>
                </group>
                
                <group string="Informations calculées" col="4">
                    <field name="total_packages" readonly="1"/>
                    <field name="quantity_per_pallet" readonly="1"/>
                </group>
                
                <footer>
                    <button name="action_generate_lots" string="Générer" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Ignorer" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action pour ouvrir le wizard -->
    <record id="action_stock_lot_reception_wizard" model="ir.actions.act_window">
        <field name="name">Nouveau Lot</field>
        <field name="res_model">stock.lot.reception.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'default_move_id': active_id}</field>
    </record>
</odoo>
