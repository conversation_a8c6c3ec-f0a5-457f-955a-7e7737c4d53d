from odoo import fields, models, api


class StockLot(models.Model):
    _inherit = "stock.lot"

    # Champs pour la gestion des réceptions avec palettes
    supplier_lot_number = fields.Char(
        string="Numéro de lot fournisseur",
        help="Numéro de lot fourni par le fournisseur"
    )
    
    manufacturing_date = fields.Date(
        string="Date de fabrication",
        help="Date de fabrication du produit"
    )
    
    production_order_number = fields.Char(
        string="Numéro d'OF",
        help="Numéro d'ordre de fabrication"
    )
    
    use_by_date = fields.Date(
        string="DLU (Date Limite d'Utilisation)",
        help="Date limite d'utilisation du produit"
    )
    
    reception_type = fields.Selection([
        ('trading', 'Négoce'),
        ('raw_material', 'Matière première'),
        ('packaging', 'Emballage'),
    ], string="Type de réception", help="Type de produit reçu")
    
    package_type_id = fields.Many2one(
        'product.packaging',
        string="Type de colis",
        help="Type de conditionnement (carton, sac, etc.)"
    )
    
    packages_per_pallet = fields.Integer(
        string="Nombre de colis par palette",
        help="Nombre de colis contenus dans une palette"
    )
    
    pallet_count = fields.Integer(
        string="Nombre de palettes",
        help="Nombre total de palettes pour ce lot"
    )
    
    is_complete_pallet = fields.Boolean(
        string="Palette complète",
        default=True,
        help="Indique si la palette est complète ou partielle"
    )
    
    # Champs calculés pour affichage
    total_packages = fields.Integer(
        string="Total colis",
        compute="_compute_total_packages",
        help="Nombre total de colis (palettes × colis par palette)"
    )
    
    @api.depends('pallet_count', 'packages_per_pallet')
    def _compute_total_packages(self):
        for lot in self:
            lot.total_packages = lot.pallet_count * lot.packages_per_pallet
