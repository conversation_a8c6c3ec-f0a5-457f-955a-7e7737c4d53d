# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_custom
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-19 11:05+0000\n"
"PO-Revision-Date: 2025-08-19 11:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__dluo_date
msgid "DLUO"
msgstr "DLUO"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__pallet_status__done
msgid "Done"
msgstr "Terminé"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__pallet_status__in_progress
msgid "In Progress"
msgstr "En cours"

#. module: stock_custom
#: model:ir.model.fields,help:stock_custom.field_stock_quant_package__pallet_status
msgid "Indicates whether the pallet is completed or not."
msgstr "Indique si la palette est terminée ou non."


#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__package_count
msgid "Number of Packages"
msgstr "Nombre de Palette"

#. module: stock_custom
#: model:ir.model,name:stock_custom.model_stock_quant_package
msgid "Packages"
msgstr "Palette"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__product_type__packaging
msgid "Packaging"
msgstr "Emballage"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__pallet_status
msgid "Pallet Status"
msgstr "Pallet Status"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__product_type
msgid "Product Type"
msgstr "Type de produit"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__product_type__raw_material
msgid "Raw Material"
msgstr "Matière Première"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__lot_supplier_number
msgid "Supplier Lot Number"
msgstr "Numéro de lot fournisseur"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__product_type__trading
msgid "Trading"
msgstr "Négoce"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_package
msgid "Packages"
msgstr "Palettes"
