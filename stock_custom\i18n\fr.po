# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_custom
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-19 11:05+0000\n"
"PO-Revision-Date: 2025-08-19 11:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__supplier_lot_number
msgid "Numéro de lot fournisseur"
msgstr "Numéro de lot fournisseur"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__manufacturing_date
msgid "Date de fabrication"
msgstr "Date de fabrication"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__production_order_number
msgid "Numéro d'OF"
msgstr "Numéro d'OF"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__use_by_date
msgid "DLU (Date Limite d'Utilisation)"
msgstr "DLU (Date Limite d'Utilisation)"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__reception_type
msgid "Type de réception"
msgstr "Type de réception"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_lot__reception_type__trading
msgid "Négoce"
msgstr "Négoce"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_lot__reception_type__raw_material
msgid "Matière première"
msgstr "Matière première"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_lot__reception_type__packaging
msgid "Emballage"
msgstr "Emballage"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__package_type_id
msgid "Type de colis"
msgstr "Type de colis"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__packages_per_pallet
msgid "Nombre de colis par palette"
msgstr "Nombre de colis par palette"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__pallet_count
msgid "Nombre de palettes"
msgstr "Nombre de palettes"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__is_complete_pallet
msgid "Palette complète"
msgstr "Palette complète"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_lot__total_packages
msgid "Total colis"
msgstr "Total colis"

#. module: stock_custom
#: model:ir.actions.act_window,name:stock_custom.action_stock_lot_reception_wizard
msgid "Nouveau Lot"
msgstr "Nouveau Lot"

#. module: stock_custom
#: model:ir.ui.view,arch_db:stock_custom.view_stock_lot_reception_wizard_form
msgid "Générer des numéros de lot"
msgstr "Générer des numéros de lot"

#. module: stock_custom
#: model:ir.ui.view,arch_db:stock_custom.view_stock_lot_reception_wizard_form
msgid "Générer"
msgstr "Générer"

#. module: stock_custom
#: model:ir.ui.view,arch_db:stock_custom.view_stock_lot_reception_wizard_form
msgid "Ignorer"
msgstr "Ignorer"

#. module: stock_custom
#: code:addons/stock_custom/wizard/stock_lot_reception_wizard.py:0
#, python-format
msgid "La quantité reçue doit être positive."
msgstr "La quantité reçue doit être positive."

#. module: stock_custom
#: code:addons/stock_custom/wizard/stock_lot_reception_wizard.py:0
#, python-format
msgid "Le nombre de palettes doit être positif."
msgstr "Le nombre de palettes doit être positif."

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__dluo_date
msgid "DLUO"
msgstr "DLUO"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__pallet_status__done
msgid "Done"
msgstr "Terminé"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__pallet_status__in_progress
msgid "In Progress"
msgstr "En cours"

#. module: stock_custom
#: model:ir.model.fields,help:stock_custom.field_stock_quant_package__pallet_status
msgid "Indicates whether the pallet is completed or not."
msgstr "Indique si la palette est terminée ou non."


#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__package_count
msgid "Number of Packages"
msgstr "Nombre de Palette"

#. module: stock_custom
#: model:ir.model,name:stock_custom.model_stock_quant_package
msgid "Packages"
msgstr "Palette"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__product_type__packaging
msgid "Packaging"
msgstr "Emballage"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__pallet_status
msgid "Pallet Status"
msgstr "Pallet Status"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__product_type
msgid "Product Type"
msgstr "Type de produit"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__product_type__raw_material
msgid "Raw Material"
msgstr "Matière Première"

#. module: stock_custom
#: model:ir.model.fields,field_description:stock_custom.field_stock_quant_package__lot_supplier_number
msgid "Supplier Lot Number"
msgstr "Numéro de lot fournisseur"

#. module: stock_custom
#: model:ir.model.fields.selection,name:stock_custom.selection__stock_quant_package__product_type__trading
msgid "Trading"
msgstr "Négoce"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_package
msgid "Packages"
msgstr "Palettes"
