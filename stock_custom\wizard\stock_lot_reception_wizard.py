# Ce fichier est déjà créé dans models/stock_lot_reception_wizard.py
# Il faut le déplacer ici pour respecter la structure Odoo

from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class StockLotReceptionWizard(models.TransientModel):
    _name = 'stock.lot.reception.wizard'
    _description = 'Wizard pour créer des lots de réception avec palettes'

    # Référence au mouvement de stock
    move_id = fields.Many2one(
        'stock.move',
        string="Mouvement de stock",
        required=True
    )
    
    product_id = fields.Many2one(
        'product.product',
        string="Produit",
        related='move_id.product_id',
        readonly=True
    )
    
    # Informations du lot
    first_lot_number = fields.Char(
        string="Premier numéro de lot",
        default="Lot générer auto",
        help="Premier numéro de lot à générer"
    )
    
    quantity_received = fields.Float(
        string="Quantité reçue",
        required=True,
        help="Quantité totale reçue"
    )
    
    supplier_lot_number = fields.Char(
        string="Numéro de lot fournisseur",
        required=True,
        help="Numéro de lot fourni par le fournisseur"
    )
    
    manufacturing_date = fields.Date(
        string="Date de fabrication",
        help="Date de fabrication du produit"
    )
    
    production_order_number = fields.Char(
        string="Numéro d'OF",
        help="Numéro d'ordre de fabrication"
    )
    
    use_by_date = fields.Date(
        string="DLU (Date Limite d'Utilisation)",
        required=True,
        help="Date limite d'utilisation du produit"
    )
    
    reception_type = fields.Selection([
        ('trading', 'Négoce'),
        ('raw_material', 'Matière première'),
        ('packaging', 'Emballage'),
    ], string="Type de réception", required=True, help="Type de produit reçu")
    
    # Informations de conditionnement
    package_type_id = fields.Many2one(
        'product.packaging',
        string="Type de colis",
        domain="[('product_id', '=', product_id)]",
        help="Type de conditionnement (carton, sac, etc.)"
    )
    
    packages_per_pallet = fields.Integer(
        string="Nombre de colis par palette",
        help="Nombre de colis contenus dans une palette"
    )
    
    pallet_count = fields.Integer(
        string="Nombre de palettes",
        required=True,
        default=1,
        help="Nombre total de palettes pour ce lot"
    )
    
    is_complete_pallet = fields.Boolean(
        string="Palette complète",
        default=True,
        help="Indique si la palette est complète ou partielle"
    )
    
    # Champs calculés
    total_packages = fields.Integer(
        string="Total colis",
        compute="_compute_total_packages",
        help="Nombre total de colis (palettes × colis par palette)"
    )
    
    quantity_per_pallet = fields.Float(
        string="Quantité par palette",
        compute="_compute_quantity_per_pallet",
        help="Quantité par palette"
    )

    @api.depends('pallet_count', 'packages_per_pallet')
    def _compute_total_packages(self):
        for wizard in self:
            wizard.total_packages = wizard.pallet_count * wizard.packages_per_pallet

    @api.depends('quantity_received', 'pallet_count')
    def _compute_quantity_per_pallet(self):
        for wizard in self:
            if wizard.pallet_count > 0:
                wizard.quantity_per_pallet = wizard.quantity_received / wizard.pallet_count
            else:
                wizard.quantity_per_pallet = 0

    @api.onchange('product_id')
    def _onchange_product_id(self):
        """Sélectionner automatiquement le premier type de conditionnement"""
        if self.product_id:
            packaging = self.env['product.packaging'].search([
                ('product_id', '=', self.product_id.id)
            ], limit=1)
            if packaging:
                self.package_type_id = packaging.id
                self.packages_per_pallet = packaging.qty or 1

    @api.onchange('package_type_id')
    def _onchange_package_type_id(self):
        """Calculer automatiquement le nombre de colis par palette"""
        if self.package_type_id:
            self.packages_per_pallet = self.package_type_id.qty or 1

    def action_generate_lots(self):
        """Générer les lots et les lignes de mouvement"""
        self.ensure_one()
        
        if self.quantity_received <= 0:
            raise ValidationError(_("La quantité reçue doit être positive."))
        
        if self.pallet_count <= 0:
            raise ValidationError(_("Le nombre de palettes doit être positif."))

        # Générer les lots pour chaque palette
        move_line_vals = []
        
        for pallet_num in range(1, self.pallet_count + 1):
            # Calculer la quantité pour cette palette
            if pallet_num == self.pallet_count and not self.is_complete_pallet:
                # Dernière palette potentiellement partielle
                remaining_qty = self.quantity_received - (self.quantity_per_pallet * (pallet_num - 1))
                pallet_qty = remaining_qty
            else:
                pallet_qty = self.quantity_per_pallet
            
            # Créer le lot
            lot_name = self._generate_lot_name(pallet_num)
            lot_vals = {
                'name': lot_name,
                'product_id': self.product_id.id,
                'company_id': self.move_id.company_id.id,
                'supplier_lot_number': self.supplier_lot_number,
                'manufacturing_date': self.manufacturing_date,
                'production_order_number': self.production_order_number,
                'use_by_date': self.use_by_date,
                'reception_type': self.reception_type,
                'package_type_id': self.package_type_id.id if self.package_type_id else False,
                'packages_per_pallet': self.packages_per_pallet,
                'pallet_count': 1,  # Chaque lot représente une palette
                'is_complete_pallet': self.is_complete_pallet if pallet_num == self.pallet_count else True,
            }
            
            lot = self.env['stock.lot'].create(lot_vals)
            
            # Créer la ligne de mouvement
            move_line_vals.append({
                'move_id': self.move_id.id,
                'product_id': self.product_id.id,
                'product_uom_id': self.product_id.uom_id.id,
                'location_id': self.move_id.location_id.id,
                'location_dest_id': self.move_id.location_dest_id.id,
                'lot_id': lot.id,
                'quantity': pallet_qty,
                'picked': True,
            })

        # Créer les lignes de mouvement
        self.env['stock.move.line'].create(move_line_vals)
        
        return {'type': 'ir.actions.act_window_close'}

    def _generate_lot_name(self, pallet_num):
        """Générer le nom du lot avec DLU et numéro de lot fournisseur"""
        if self.first_lot_number == "Lot générer auto":
            # Format: SUPPLIER_LOT_DLU_PALETTE_NUM
            dlu_str = self.use_by_date.strftime('%d%m%Y') if self.use_by_date else ''
            return f"{self.supplier_lot_number}_{dlu_str}_palette{pallet_num}"
        else:
            # Utiliser le format spécifié par l'utilisateur
            return f"{self.first_lot_number}_{pallet_num}"
