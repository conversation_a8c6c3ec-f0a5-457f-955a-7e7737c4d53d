from odoo import fields, models


class StockQuantPackage(models.Model):
    _inherit = "stock.quant.package"

    pallet_status = fields.Selection(
        [("in_progress", "In Progress"), ("done", "Done")],
        help="Indicates whether the pallet is completed or not.",
    )

    package_count = fields.Many2one(
        "product.packaging", string="Number of Packages", ondelete="cascade"
    )

    product_type = fields.Selection(
        [
            ("trading", "Trading"),
            ("raw_material", "Raw Material"),
            ("packaging", "Packaging"),
        ],
    )

