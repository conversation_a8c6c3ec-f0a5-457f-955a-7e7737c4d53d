<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extension de la vue formulaire des lots -->
    <record id="view_production_lot_form_custom" model="ir.ui.view">
        <field name="name">stock.lot.form.custom</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='ref']" position="after">
                <field name="supplier_lot_number"/>
                <field name="reception_type"/>
            </xpath>
            
            <xpath expr="//field[@name='note']" position="before">
                <group string="Informations de réception" col="4">
                    <field name="manufacturing_date"/>
                    <field name="production_order_number"/>
                    <field name="use_by_date"/>
                    <field name="package_type_id"/>
                    <field name="packages_per_pallet"/>
                    <field name="pallet_count"/>
                    <field name="is_complete_pallet"/>
                    <field name="total_packages"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Extension de la vue liste des lots -->
    <record id="view_production_lot_tree_custom" model="ir.ui.view">
        <field name="name">stock.lot.tree.custom</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='ref']" position="after">
                <field name="supplier_lot_number" optional="show"/>
                <field name="reception_type" optional="show"/>
                <field name="use_by_date" optional="show"/>
                <field name="pallet_count" optional="show"/>
                <field name="is_complete_pallet" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
