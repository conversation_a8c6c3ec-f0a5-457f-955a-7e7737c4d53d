from odoo import models, _


class StockMove(models.Model):
    _inherit = 'stock.move'

    def action_open_new_lot_wizard(self):
        """Ouvrir le wizard pour créer un nouveau lot de réception"""
        self.ensure_one()
        
        return {
            'name': _('Nouveau Lot'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.lot.reception.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_move_id': self.id,
                'default_quantity_received': self.product_uom_qty,
            }
        }
