#!/usr/bin/env python3
"""
Script de démonstration pour le module stock_custom
Ce script montre comment utiliser la fonctionnalité de réception avec palettes
"""

def demo_usage():
    """
    Exemple d'utilisation du module stock_custom
    
    Ce script simule l'utilisation du wizard de création de lots de réception
    """
    
    print("=== Démonstration du module Stock Custom ===")
    print()
    
    print("1. Scénario : Réception de 217 kg d'Arachide Purée")
    print("   - Produit : [000012] Arachide Purée")
    print("   - Quantité demandée : 217,00 kg")
    print("   - Fournisseur : Arachide")
    print()
    
    print("2. Ouverture du wizard 'Nouveau Lot'")
    print("   - Clic sur le bouton 'Nouveau Lot' à côté des boutons existants")
    print()
    
    print("3. Saisie des informations dans le wizard :")
    wizard_data = {
        'Premier numéro de lot': 'Lot générer auto',
        'Quantité reçue': '200 kg',
        'Numéro de lot fournisseur': '17171717',
        'DLU': '25/12/2026',
        'Type de réception': 'Matière première',
        'Date de fabrication': '18/07/2025',
        'Numéro d\'OF': '',
        'Type de colis': 'Carton (sélectionné automatiquement)',
        'Nombre de colis par palette': '48 (calculé automatiquement)',
        'Nombre de palettes': '2',
        'Palette complète': 'Oui ✓'
    }
    
    for key, value in wizard_data.items():
        print(f"   - {key}: {value}")
    print()
    
    print("4. Informations calculées automatiquement :")
    print("   - Total colis: 96 (2 palettes × 48 colis)")
    print("   - Quantité par palette: 100,00 kg")
    print()
    
    print("5. Clic sur 'Générer'")
    print()
    
    print("6. Résultat de la génération :")
    print("   Création de 2 lots automatiquement :")
    print("   - Lot 1: 17171717_25122026_palette1 (100,00 kg)")
    print("   - Lot 2: 17171717_25122026_palette2 (100,00 kg)")
    print()
    
    print("7. Lignes de mouvement créées :")
    print("   - Ligne 1: Lot générer auto, JH/Stock, palette1, 100,00 kg")
    print("   - Ligne 2: Lot générer auto, JH/Stock, palette2, 100,00 kg")
    print("   - Total: 200,00 kg")
    print()
    
    print("8. Informations stockées pour chaque lot :")
    lot_info = {
        'Numéro de lot fournisseur': '17171717',
        'DLU': '25/12/2026',
        'Type de réception': 'Matière première',
        'Date de fabrication': '18/07/2025',
        'Type de colis': 'Carton',
        'Nombre de colis par palette': '48',
        'Nombre de palettes': '1 (par lot)',
        'Palette complète': 'Oui',
        'Total colis': '48 (par lot)'
    }
    
    for key, value in lot_info.items():
        print(f"   - {key}: {value}")
    print()
    
    print("9. Répétition pour les autres lots/palettes :")
    print("   - Possibilité de créer un nouveau lot pour la palette restante (17 kg)")
    print("   - Possibilité de créer des lots pour d'autres numéros de lot fournisseur")
    print()
    
    print("10. Passage à la ligne suivante du bon de réception")
    print("    - Traitement de [000013] avec la même méthode")
    print()
    
    print("=== Avantages de cette approche ===")
    advantages = [
        "Accélération de la réception (palettes complètes uniquement)",
        "Traçabilité complète avec numéro de lot fournisseur et DLU",
        "Gestion automatique du colisage",
        "Flexibilité pour palettes partielles",
        "Intégration native avec le système de stock Odoo",
        "Respect des règles métier spécifiques"
    ]
    
    for i, advantage in enumerate(advantages, 1):
        print(f"{i}. {advantage}")
    print()
    
    print("=== Fin de la démonstration ===")


if __name__ == "__main__":
    demo_usage()
