from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import date, timedelta


class TestStockLotReception(TransactionCase):

    def setUp(self):
        super().setUp()
        
        # Créer un produit de test avec tracking par lot
        self.product = self.env['product.product'].create({
            'name': 'Test Product',
            'type': 'product',
            'tracking': 'lot',
            'is_storable': True,
        })
        
        # Créer un conditionnement de test
        self.packaging = self.env['product.packaging'].create({
            'name': 'Carton',
            'product_id': self.product.id,
            'qty': 48,  # 48 unités par carton
        })
        
        # Créer des emplacements de test
        self.location_supplier = self.env['stock.location'].create({
            'name': 'Supplier Location',
            'usage': 'supplier',
        })
        
        self.location_stock = self.env['stock.location'].create({
            'name': 'Stock Location',
            'usage': 'internal',
        })
        
        # Créer un mouvement de stock de test
        self.stock_move = self.env['stock.move'].create({
            'name': 'Test Reception',
            'product_id': self.product.id,
            'product_uom_qty': 200.0,
            'product_uom': self.product.uom_id.id,
            'location_id': self.location_supplier.id,
            'location_dest_id': self.location_stock.id,
        })

    def test_wizard_creation(self):
        """Test la création du wizard avec les valeurs par défaut"""
        wizard = self.env['stock.lot.reception.wizard'].create({
            'move_id': self.stock_move.id,
            'quantity_received': 200.0,
            'supplier_lot_number': 'FOURNISSEUR123',
            'use_by_date': date.today() + timedelta(days=365),
            'reception_type': 'raw_material',
            'pallet_count': 2,
        })
        
        self.assertEqual(wizard.product_id, self.product)
        self.assertEqual(wizard.quantity_per_pallet, 100.0)
        self.assertTrue(wizard.is_complete_pallet)

    def test_lot_generation(self):
        """Test la génération automatique des lots"""
        wizard = self.env['stock.lot.reception.wizard'].create({
            'move_id': self.stock_move.id,
            'quantity_received': 200.0,
            'supplier_lot_number': 'FOURNISSEUR123',
            'use_by_date': date.today() + timedelta(days=365),
            'reception_type': 'raw_material',
            'package_type_id': self.packaging.id,
            'packages_per_pallet': 48,
            'pallet_count': 2,
        })
        
        # Exécuter la génération
        wizard.action_generate_lots()
        
        # Vérifier que les lots ont été créés
        lots = self.env['stock.lot'].search([
            ('product_id', '=', self.product.id),
            ('supplier_lot_number', '=', 'FOURNISSEUR123')
        ])
        
        self.assertEqual(len(lots), 2)  # 2 palettes = 2 lots
        
        # Vérifier que les lignes de mouvement ont été créées
        move_lines = self.stock_move.move_line_ids
        self.assertEqual(len(move_lines), 2)
        
        # Vérifier les quantités
        total_qty = sum(move_lines.mapped('quantity'))
        self.assertEqual(total_qty, 200.0)

    def test_lot_name_generation(self):
        """Test la génération des noms de lots"""
        use_by_date = date(2025, 12, 31)
        wizard = self.env['stock.lot.reception.wizard'].create({
            'move_id': self.stock_move.id,
            'quantity_received': 100.0,
            'supplier_lot_number': 'FOURNISSEUR123',
            'use_by_date': use_by_date,
            'reception_type': 'raw_material',
            'pallet_count': 1,
        })
        
        # Test de génération automatique du nom
        lot_name = wizard._generate_lot_name(1)
        expected_name = "FOURNISSEUR123_31122025_palette1"
        self.assertEqual(lot_name, expected_name)

    def test_validation_errors(self):
        """Test les validations du wizard"""
        wizard = self.env['stock.lot.reception.wizard'].create({
            'move_id': self.stock_move.id,
            'quantity_received': 0.0,  # Quantité invalide
            'supplier_lot_number': 'FOURNISSEUR123',
            'use_by_date': date.today() + timedelta(days=365),
            'reception_type': 'raw_material',
            'pallet_count': 1,
        })
        
        # Doit lever une exception pour quantité invalide
        with self.assertRaises(ValidationError):
            wizard.action_generate_lots()
        
        # Test avec nombre de palettes invalide
        wizard.quantity_received = 100.0
        wizard.pallet_count = 0
        
        with self.assertRaises(ValidationError):
            wizard.action_generate_lots()

    def test_partial_pallet(self):
        """Test la gestion des palettes partielles"""
        wizard = self.env['stock.lot.reception.wizard'].create({
            'move_id': self.stock_move.id,
            'quantity_received': 150.0,  # 1.5 palette
            'supplier_lot_number': 'FOURNISSEUR123',
            'use_by_date': date.today() + timedelta(days=365),
            'reception_type': 'raw_material',
            'pallet_count': 2,
            'is_complete_pallet': False,  # Dernière palette partielle
        })
        
        wizard.action_generate_lots()
        
        # Vérifier les quantités des lignes de mouvement
        move_lines = self.stock_move.move_line_ids.sorted('quantity', reverse=True)
        self.assertEqual(len(move_lines), 2)
        self.assertEqual(move_lines[0].quantity, 75.0)  # Première palette
        self.assertEqual(move_lines[1].quantity, 75.0)  # Deuxième palette (partielle)

    def test_stock_lot_fields(self):
        """Test les nouveaux champs du modèle stock.lot"""
        lot = self.env['stock.lot'].create({
            'name': 'TEST_LOT_001',
            'product_id': self.product.id,
            'supplier_lot_number': 'FOURNISSEUR123',
            'reception_type': 'raw_material',
            'use_by_date': date.today() + timedelta(days=365),
            'manufacturing_date': date.today(),
            'production_order_number': 'OF001',
            'package_type_id': self.packaging.id,
            'packages_per_pallet': 48,
            'pallet_count': 2,
            'is_complete_pallet': True,
        })
        
        # Vérifier le calcul du total de colis
        self.assertEqual(lot.total_packages, 96)  # 2 palettes × 48 colis
        
        # Vérifier tous les champs
        self.assertEqual(lot.supplier_lot_number, 'FOURNISSEUR123')
        self.assertEqual(lot.reception_type, 'raw_material')
        self.assertEqual(lot.production_order_number, 'OF001')
        self.assertTrue(lot.is_complete_pallet)
