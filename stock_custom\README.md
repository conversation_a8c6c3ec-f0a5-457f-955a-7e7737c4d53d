# Module Stock Custom - Gestion des Réceptions avec Palettes

## Description

Ce module étend les fonctionnalités de stock d'Odoo pour gérer les réceptions de marchandises sur palettes avec des règles de gestion spécifiques.

## Fonctionnalités

### Règles de gestion à la réception

#### Produits reçus sur palettes
- Tous les produits sont réceptionnés physiquement sur des palettes
- Les palettes ne sont pas toujours pleines
- Possibilité d'indiquer le nombre de palettes partielles ou complètes

#### Conditionnements unitaires
- Pour certains produits (négoce et matières premières), la marchandise est livrée en sacs ou cartons
- Saisie du type de conditionnement unitaire (sac, carton, etc.)
- Saisie du nombre d'unités par palette

#### Accélération de la réception
- Réception uniquement des palettes complètes finalisées de même lot
- Bouton "Nouveau Lot" ajouté à côté des boutons existants

### Wizard "Nouveau Lot"

Le wizard permet de saisir les informations suivantes :

#### Informations obligatoires
- **Type de réception** : <PERSON><PERSON><PERSON><PERSON>, Matière première, ou Emballage
- **Quantité à réceptionner**
- **Numéro de lot fournisseur**
- **DLU (Date Limite d'Utilisation)**
- **Nombre de palettes**

#### Informations optionnelles
- **Date de fabrication**
- **Numéro d'OF (Ordre de Fabrication)**
- **Type de colis** : Sélection automatique du premier type de conditionnement lié au produit
- **Nombre de colis par palette** : Calculé automatiquement, modifiable
- **Case à cocher** : Palette complète ou non

### Génération automatique

#### Numéros de lot
- Création automatique avec format : `{numéro_lot_fournisseur}_{DLU}_{palette_numéro}`
- Intégration des informations DLU et numéro de lot fournisseur

#### Colisage
- Génération avec les informations suivantes :
  - Numéro de lot
  - Nombre de colis
  - Lien avec le type de conditionnement
  - Type de produit (négoce, matière première, emballage)

## Installation

1. Copier le module dans le répertoire des addons Odoo
2. Mettre à jour la liste des modules
3. Installer le module "STOCK CUSTOM"

## Utilisation

1. Aller dans Inventaire > Opérations > Réceptions
2. Ouvrir une réception
3. Cliquer sur le bouton de détails d'un mouvement de stock
4. Utiliser le bouton "Nouveau Lot" à côté des boutons existants
5. Remplir le wizard avec les informations requises
6. Cliquer sur "Générer"

## Modèles étendus

### stock.lot
Nouveaux champs ajoutés :
- `supplier_lot_number` : Numéro de lot fournisseur
- `manufacturing_date` : Date de fabrication
- `production_order_number` : Numéro d'OF
- `use_by_date` : Date limite d'utilisation
- `reception_type` : Type de réception (négoce/matière première/emballage)
- `package_type_id` : Type de colis
- `packages_per_pallet` : Nombre de colis par palette
- `pallet_count` : Nombre de palettes
- `is_complete_pallet` : Palette complète ou non
- `total_packages` : Total colis (calculé)

### stock.move
Nouvelle méthode ajoutée :
- `action_open_new_lot_wizard()` : Ouvre le wizard de création de lot

## Wizard

### stock.lot.reception.wizard
Modèle transient pour la création de lots de réception avec toutes les informations nécessaires.

## Tests

Le module inclut des tests unitaires pour valider :
- La création du wizard
- La génération des lots
- La génération des noms de lots
- Les validations
- La gestion des palettes partielles
- Les nouveaux champs du modèle stock.lot

Pour exécuter les tests :
```bash
odoo-bin -d test_db -i stock_custom --test-enable --stop-after-init
```

## Maintainer

This module is maintained by the OKTEO. For more information, visit the
(https://www.okteo.fr/).

## Licence

Other proprietary
